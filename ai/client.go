package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"reflect"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"github.com/mogan/tag-assistant/config"
	"github.com/mogan/tag-assistant/db"
	"github.com/mogan/tag-assistant/utils"
)

const (
	deepSeekAPIURL = "https://api.deepseek.com/v1/chat/completions"
)

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

var (
	ErrNoAPIKey           = fmt.Errorf("未配置DeepSeek API密钥")
	ErrMarshalRequestBody = fmt.Errorf("请求体序列化失败")
	ErrCreateRequest      = fmt.Errorf("创建请求失败")
	ErrSendRequest        = fmt.Errorf("发送请求失败")
	ErrReadResponse       = fmt.Errorf("读取响应失败")
	ErrUnmarshalResponse  = fmt.Errorf("解析响应失败")
	ErrNoChoicesGenerated = fmt.Errorf("未生成任何标签")
	ErrNoValidJSON        = fmt.Errorf("未找到有效的JSON响应")
	ErrUnmarshalJSON      = fmt.Errorf("解析JSON失败")
	ErrMaxRetriesExceeded = fmt.Errorf("API请求失败，已达到最大重试次数")
	ErrAPIFailure         = fmt.Errorf("API请求失败")
	ErrRateLimitExceeded  = fmt.Errorf("API请求频率限制")
	ErrServerError        = fmt.Errorf("API服务器错误")
	ErrDataCorruption     = fmt.Errorf("响应数据损坏")
)

// AIClient 定义了AI客户端需要实现的方法
type AIClient interface {
	GenerateTags(ctx context.Context, product db.Product, samples []db.Product) (db.Product, []string, error)
	BatchGenerateTags(ctx context.Context, products []db.Product, samples []db.Product) ([]db.Product, [][]string, []error)
}

type DeepSeekClient struct {
	apiKey    string
	chatModel string // Chat模型名称
}

func NewDeepSeekClient() *DeepSeekClient {
	cfg := config.GetConfig()
	apiKey := ""
	if cfg != nil {
		apiKey = cfg.DeepSeekAPIKey
	} else {
		fmt.Println("警告：配置未初始化，使用空API密钥")
	}
	return &DeepSeekClient{
		apiKey:    apiKey,
		chatModel: "deepseek-chat",
	}
}

// 获取当前使用的模型名称
func (c *DeepSeekClient) getCurrentModel() string {
	return c.chatModel
}

type deepSeekRequest struct {
	Model             string         `json:"model"`
	Messages          []message      `json:"messages"`
	Temperature       float32        `json:"temperature"`
	MaxTokens         int            `json:"max_tokens"`
	ResponseFormat    responseFormat `json:"response_format"`
	ExtraOptions      extraOptions   `json:"extra_options"`
	TopP              float32        `json:"top_p,omitempty"`
	RepetitionPenalty float32        `json:"repetition_penalty,omitempty"`
}

type message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type responseFormat struct {
	Type string `json:"type"`
}

type extraOptions struct {
	IncludeThoughts bool `json:"include_thoughts,omitempty"`
}

type deepSeekResponse struct {
	Choices []choice `json:"choices"`
}

type choice struct {
	Message message `json:"message"`
}

type aiTags struct {
	CoreTag     string   `json:"core_tag"`
	Scene       string   `json:"scene"`
	Crowd       string   `json:"crowd"`
	Brand       string   `json:"brand"`
	Spec        string   `json:"spec"`
	FeatureTags []string `json:"feature_tags"`
	Category2   []string `json:"category2"`
}

type APIMetrics struct {
	TotalRequests int
	TotalTokens   int
	sync.Mutex
}

var apiMetrics = &APIMetrics{}

type MedicalTagConfig struct {
	Temperature       float32
	TopP              float32
	MaxTokens         int
	RepetitionPenalty float32
}

// ====================== 辅助函数定义 ======================

func GetMedicalGoodsConfig() MedicalTagConfig {
	return MedicalTagConfig{
		Temperature:       0.35,
		TopP:              0.15,
		MaxTokens:         6000,
		RepetitionPenalty: 1.15,
	}
}

func parseSingleResponse(content string) (aiTags, error) {
	startIdx := strings.Index(content, "{")
	endIdx := strings.LastIndex(content, "}")
	if startIdx == -1 || endIdx == -1 {
		return aiTags{}, fmt.Errorf("%s: %s", ErrNoValidJSON, content)
	}
	jsonStr := content[startIdx : endIdx+1]

	var tags aiTags
	if err := json.Unmarshal([]byte(jsonStr), &tags); err == nil {
		// 强制特征标签只保留一个
		if len(tags.FeatureTags) > 1 {
			fmt.Printf("AI返回了多个特征标签: %v, 只保留第一个\n", tags.FeatureTags)
			tags.FeatureTags = tags.FeatureTags[:1]
		}
		return tags, nil
	}

	// 回退解析结构体（包含特征标签）
	var fallback struct {
		CoreTag     string   `json:"core_tag"`
		Scene       string   `json:"scene"`
		Crowd       string   `json:"crowd"`
		Brand       string   `json:"brand"`
		Spec        string   `json:"spec"`
		Category2   string   `json:"category2"`
		FeatureTags []string `json:"feature_tags"` // 包含特征标签字段
	}

	if err := json.Unmarshal([]byte(jsonStr), &fallback); err != nil {
		return aiTags{}, fmt.Errorf("%w: %v, 原始内容: %s", ErrUnmarshalJSON, err, jsonStr)
	}

	categories := []string{}
	if fallback.Category2 != "" {
		categories = strings.Split(fallback.Category2, ",")
		for i, cat := range categories {
			categories[i] = strings.TrimSpace(cat)
		}
	}

	// 确保特征标签只取第一个
	featureTags := []string{}
	if len(fallback.FeatureTags) > 0 {
		featureTags = []string{fallback.FeatureTags[0]}
	}

	tags = aiTags{
		CoreTag:     fallback.CoreTag,
		Scene:       fallback.Scene,
		Crowd:       fallback.Crowd,
		Brand:       fallback.Brand,
		Spec:        fallback.Spec,
		Category2:   categories,
		FeatureTags: featureTags, // 设置特征标签
	}

	return tags, nil
}

func processTags(product db.Product, tags aiTags, samples []db.Product) (db.Product, []string) {

	// 强化特征标签处理（只保留第一个）
	if len(tags.FeatureTags) > 0 {
		// 只保留第一个特征标签
		feature := tags.FeatureTags[0]
		product.FeatureTags = []string{feature}
	} else {
		product.FeatureTags = []string{}
	}

	product.CoreTag = tags.CoreTag
	product.Scene = tags.Scene
	product.Crowd = tags.Crowd
	product.Spec = tags.Spec
	product.Category2 = tags.Category2

	// === 增强监控：品牌处理优化 ===
	fmt.Printf("[processTags-2.0] 开始品牌处理优化\n")
	brand := tags.Brand
	fmt.Printf("[processTags-2.1] 初始AI品牌: '%s'\n", brand)

	// === 深度监控：AI品牌有效性检查 ===
	fmt.Printf("[processTags-2.2] 🔍 AI品牌有效性检查\n")
	aiBrandValid := brand != "" && brand != "无品牌" && brand != "未知品牌" && brand != "无"
	fmt.Printf("[processTags-2.3] AI品牌有效性: %t\n", aiBrandValid)
	if !aiBrandValid {
		fmt.Printf("[processTags-2.4] ⚠️ AI品牌无效，原因: '%s'\n", brand)
	}

	// === 简化品牌选择逻辑 ===
	fmt.Printf("[processTags-3.0] 开始品牌选择逻辑\n")
	fmt.Printf("[processTags-3.1] AI返回品牌: '%s'\n", brand)

	// 直接使用AI提取的品牌，无需重复验证
	if brand != "" && brand != "无品牌" && brand != "未知品牌" && brand != "无" {
		fmt.Printf("[processTags-3.2] ✅ 使用AI品牌: '%s'\n", brand)
		fmt.Printf("[processTags-3.2.1] 品牌来源: AI提取\n")
	} else {
		fmt.Printf("[processTags-3.3] ❌ AI品牌无效: '%s'\n", brand)
		brand = ""
		fmt.Printf("[processTags-3.3.1] 品牌来源: 无\n")
	}

	// === 增强监控：品牌标准化处理 ===
	fmt.Printf("[processTags-4.0] 开始品牌标准化，当前品牌: '%s'\n", brand)
	if brand != "" {
		fmt.Printf("[processTags-4.1] 执行品牌标准化\n")
		normalizedBrand := NormalizeBrand(brand)
		fmt.Printf("[processTags-4.2] 标准化结果: '%s' → '%s'\n", brand, normalizedBrand)
		brand = normalizedBrand
	} else {
		fmt.Printf("[processTags-4.3] 品牌为空，跳过标准化\n")
	}

	// === 增强监控：空品牌处理 ===
	fmt.Printf("[processTags-5.0] 检查空品牌，当前品牌: '%s'\n", brand)
	if brand == "" {
		brand = "无品牌"
		fmt.Printf("[processTags-5.1] ❌ 品牌为空，设置默认: '%s'\n", brand)
	} else {
		fmt.Printf("[processTags-5.2] ✅ 品牌不为空: '%s'\n", brand)
	}

	// === 🔧 品牌数据传递链路修复 ===
	fmt.Printf("[品牌修复-6.0] 开始UTF8处理，当前品牌: '%s'\n", brand)
	utf8Brand := utils.EnsureUTF8(brand)
	fmt.Printf("[品牌修复-6.1] UTF8处理结果: '%s' → '%s'\n", brand, utf8Brand)

	// === 🚨 关键修复：强制品牌设置并立即验证 ===
	fmt.Printf("[品牌修复-7.0] 🔧 强制设置品牌到产品对象\n")
	product.Brand = utf8Brand
	fmt.Printf("[品牌修复-7.1] 设置完成，立即验证: product.Brand = '%s'\n", product.Brand)

	// === 数据一致性验证 ===
	if product.Brand != utf8Brand {
		fmt.Printf("[品牌修复-7.2] ⚠️ 数据不一致！期望: '%s', 实际: '%s'\n", utf8Brand, product.Brand)
		product.Brand = utf8Brand // 强制重新设置
		fmt.Printf("[品牌修复-7.3] 🔧 强制重新设置: '%s'\n", product.Brand)
	} else {
		fmt.Printf("[品牌修复-7.4] ✅ 数据一致性验证通过: '%s'\n", product.Brand)
	}

	// === 最终验证 ===
	fmt.Printf("[品牌修复-8.0] 🎯 最终品牌验证: '%s'\n", product.Brand)
	fmt.Printf("[品牌修复-8.1] 品牌长度: %d 字符\n", len(product.Brand))
	fmt.Printf("[品牌修复-8.2] 品牌UTF8有效性: %t\n", utf8.ValidString(product.Brand))

	// === 🔧 品牌空值检测与修复 ===
	if product.Brand == "" || product.Brand == "无品牌" {
		fmt.Printf("[品牌修复-9.0] ⚠️ 检测到空品牌，尝试从AI原始数据恢复\n")
		originalBrand := tags.Brand
		fmt.Printf("[品牌修复-9.1] AI原始品牌: '%s'\n", originalBrand)
		if originalBrand != "" && originalBrand != "无品牌" && originalBrand != "未知品牌" && originalBrand != "无" {
			product.Brand = utils.EnsureUTF8(originalBrand)
			fmt.Printf("[品牌修复-9.2] 🔧 从AI原始数据恢复品牌: '%s'\n", product.Brand)
		}
	}

	fmt.Printf("--- 🔧 品牌修复完成，最终品牌: '%s' ---\n", product.Brand)

	// 如果AI未生成特征标签，使用程序提取作为后备
	if len(product.FeatureTags) == 0 {
		features := db.ExtractFeatureTags(product.ProductName, config.GetConfig())
		if len(features) > 0 {
			// 只取第一个特征标签
			product.FeatureTags = []string{features[0]}
		}
	}

	// 生成备选标签选项
	options := generateAlternativeTags(tags.CoreTag, samples)

	return product, options
}

func ExtractBrand(name string) string {
	// === 优化版品牌提取：优先使用AI，简化规则作为回退 ===
	fmt.Printf("\n🔍 [ExtractBrand-AI优先版] 开始品牌提取\n")
	fmt.Printf("[ExtractBrand-1.0] 输入产品名称: '%s'\n", name)

	// 优先使用AI提取品牌
	fmt.Printf("[ExtractBrand-2.0] 🤖 开始AI品牌提取\n")
	aiBrand := ExtractBrandWithAI(name)

	if aiBrand != "" && aiBrand != "无品牌" {
		fmt.Printf("[ExtractBrand-2.1] ✅ AI提取成功: '%s'\n", aiBrand)

		// AI提取成功后也需要进行品牌映射标准化
		fmt.Printf("[ExtractBrand-2.2] 📝 开始AI品牌映射标准化\n")
		standardizedBrand := StandardizeBrandWithMapping(aiBrand)

		if standardizedBrand != aiBrand {
			fmt.Printf("[ExtractBrand-2.3] ✅ AI品牌已标准化: '%s' → '%s'\n", aiBrand, standardizedBrand)
		} else {
			fmt.Printf("[ExtractBrand-2.4] ℹ️ AI品牌无需标准化: '%s'\n", aiBrand)
		}

		return standardizedBrand
	}

	// AI提取失败，直接返回"无品牌"
	fmt.Printf("[ExtractBrand-3.0] ❌ AI提取失败，返回无品牌\n")
	return "无品牌"
}

// ExtractBracketBrand 提取方括号中的品牌 [品牌名]
func ExtractBracketBrand(name string) string {
	// === 深度监控：ExtractBracketBrand函数 ===
	fmt.Printf("\n🔍 [ExtractBracketBrand] 开始方括号品牌提取\n")
	fmt.Printf("[ExtractBracketBrand-1.0] 输入名称: '%s'\n", name)

	// 检查是否包含方括号
	hasBrackets := strings.Contains(name, "[") && strings.Contains(name, "]")
	fmt.Printf("[ExtractBracketBrand-1.1] 包含方括号: %t\n", hasBrackets)

	if !hasBrackets {
		fmt.Printf("[ExtractBracketBrand-1.2] ❌ 不包含方括号，直接返回空\n")
		return ""
	}

	// 匹配 [品牌名] 格式
	re := regexp.MustCompile(`\[([^\]]+)\]`)
	fmt.Printf("[ExtractBracketBrand-2.0] 正则表达式: %s\n", re.String())

	matches := re.FindStringSubmatch(name)
	fmt.Printf("[ExtractBracketBrand-2.1] 正则匹配结果数量: %d\n", len(matches))

	if len(matches) >= 2 {
		rawBrand := matches[1]
		fmt.Printf("[ExtractBracketBrand-2.2] 原始匹配内容: '%s'\n", rawBrand)

		brand := strings.TrimSpace(rawBrand)
		fmt.Printf("[ExtractBracketBrand-2.3] 去空格后品牌: '%s'\n", brand)

		// 验证品牌名的有效性 - 放宽验证条件
		isValid := brand != "" && len(brand) <= 20
		fmt.Printf("[ExtractBracketBrand-3.0] 品牌有效性检查 - 非空: %t, 长度<=20: %t\n", brand != "", len(brand) <= 20)
		fmt.Printf("[ExtractBracketBrand-3.1] 品牌长度: %d\n", len(brand))

		if isValid {
			fmt.Printf("[ExtractBracketBrand-3.2] ✅ 品牌有效，返回: '%s'\n", brand)
			return brand
		} else {
			fmt.Printf("[ExtractBracketBrand-3.3] ❌ 品牌无效，原因: 空字符串或长度超过20\n")
		}
	} else {
		fmt.Printf("[ExtractBracketBrand-2.4] ❌ 正则匹配失败，matches长度: %d\n", len(matches))
	}

	fmt.Printf("[ExtractBracketBrand-4.0] ❌ 方括号品牌提取失败\n")
	return ""
}

// ExtractChineseBracketBrand 提取中文括号中的品牌 【品牌名】
func ExtractChineseBracketBrand(name string) string {
	// 匹配 【品牌名】 格式
	re := regexp.MustCompile(`【([^】]+)】`)
	matches := re.FindStringSubmatch(name)

	if len(matches) >= 2 {
		brand := strings.TrimSpace(matches[1])

		// 验证品牌名的有效性 - 放宽验证条件
		if brand != "" && len(brand) <= 20 {
			return brand
		}
	}

	return ""
}

// 提取花括号中的品牌 {品牌名}
func extractCurlyBracketBrand(name string) string {
	// 匹配 {品牌名} 格式
	re := regexp.MustCompile(`\{([^}]+)\}`)
	matches := re.FindStringSubmatch(name)

	if len(matches) >= 2 {
		brand := strings.TrimSpace(matches[1])

		// 验证品牌名的有效性
		if isValidBrand(brand) {
			return brand
		}
	}

	return ""
}

// 提取特殊引号中的品牌 「品牌名」
func extractSpecialQuoteBrand(name string) string {
	// 匹配 「品牌名」 格式
	re := regexp.MustCompile(`「([^」]+)」`)
	matches := re.FindStringSubmatch(name)

	if len(matches) >= 2 {
		brand := strings.TrimSpace(matches[1])
		// 验证品牌名的有效性
		if brand != "" && len(brand) <= 20 {
			return brand
		}
	}

	return ""
}

// 提取书名号中的品牌 〈品牌名〉
func extractAngleBracketBrand(name string) string {
	// 匹配 〈品牌名〉 格式
	re := regexp.MustCompile(`〈([^〉]+)〉`)
	matches := re.FindStringSubmatch(name)

	if len(matches) >= 2 {
		brand := strings.TrimSpace(matches[1])

		// 验证品牌名的有效性 - 放宽验证条件
		if brand != "" && len(brand) <= 20 {
			return brand
		}
	}

	return ""
}

// ExtractHeadBrand 提取开头的品牌词
func ExtractHeadBrand(name string) string {
	// 移除前缀符号和空格
	cleanName := strings.TrimSpace(name)
	cleanName = strings.TrimLeft(cleanName, "T【")

	// 常见的医疗前缀词，需要跳过
	medicalPrefixes := []string{
		"医用", "无菌", "一次性", "灭菌", "消毒", "专用", "临床", "手术",
		"外科", "内科", "妇科", "儿科", "口腔", "眼科", "耳鼻喉", "皮肤科",
		"新老包装", "升级版", "加强型", "改良型", "进口", "国产", "原装",
	}

	// 产品描述词，不应作为品牌
	productDescWords := []string{
		"鸡眼", "脚臭", "痔疮", "湿疹", "皮炎", "过敏", "感染", "发炎", "疼痛",
		"瘙痒", "红肿", "破皮", "溃疡", "疤痕", "色素", "黑头", "粉刺",
		"商丘", "云南", "北京", "上海", "广州", "深圳", "杭州", "南京", "武汉",
		"（品", "）品", "品牌", "厂家", "制造", "生产", "公司", "有限",
	}

	// 分词提取第一个词作为潜在品牌
	words := strings.Fields(cleanName)
	if len(words) == 0 {
		return ""
	}

	firstWord := words[0]

	// 检查是否以医疗前缀开头，如果是则跳过
	for _, prefix := range medicalPrefixes {
		if strings.HasPrefix(firstWord, prefix) {
			return ""
		}
	}

	// 检查是否是产品描述词，如果是则跳过
	for _, desc := range productDescWords {
		if strings.Contains(firstWord, desc) {
			return ""
		}
	}

	// 智能品牌提取：尝试提取完整的品牌名
	runes := []rune(firstWord)

	// 1. 如果是纯英文+数字组合（如"3M"），直接返回
	if isAlphaNumeric(firstWord) && len(runes) >= 2 && len(runes) <= 10 {
		return firstWord
	}

	// 2. 如果包含中文，尝试智能提取完整品牌
	if containsChinese(firstWord) {
		// 尝试匹配已知品牌模式
		knownBrandPatterns := []string{
			"云南白药", "海氏海诺", "妮蓓莉", "素秀臣氏", "winner稳健", "稳健医疗",
		}

		// 检查是否匹配已知品牌开头
		for _, pattern := range knownBrandPatterns {
			if strings.HasPrefix(cleanName, pattern) {
				return pattern
			}
		}

		// 如果长度合适，返回完整的第一个词
		if len(runes) >= 2 && len(runes) <= 8 {
			return firstWord
		}

		// 如果第一个词太长，尝试提取前面的品牌部分
		if len(runes) > 8 {
			// 尝试提取前2-4个字符作为品牌
			for i := 4; i >= 2; i-- {
				if len(runes) >= i {
					candidate := string(runes[:i])
					// 检查是否是有效的品牌候选
					if !isProductDesc(candidate) {
						return candidate
					}
				}
			}
		}
	}

	return ""
}

// 辅助函数：检查是否是字母数字组合
func isAlphaNumeric(s string) bool {
	for _, r := range s {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9')) {
			return false
		}
	}
	return true
}

// 辅助函数：检查是否包含中文
func containsChinese(s string) bool {
	for _, r := range s {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}

// 辅助函数：检查是否是产品描述词
func isProductDesc(s string) bool {
	productDescs := []string{
		"鸡眼", "脚臭", "痔疮", "湿疹", "皮炎", "过敏", "感染", "发炎",
		"商丘", "云南", "北京", "上海", "广州", "深圳", "杭州", "南京",
		"医用", "无菌", "一次性", "灭菌", "消毒", "专用", "临床",
	}

	for _, desc := range productDescs {
		if strings.Contains(s, desc) {
			return true
		}
	}
	return false
}

// 验证品牌名是否有效（优化版 - 大幅减少限制）
func isValidBrand(brand string) bool {
	// 清理品牌名中的特殊后缀
	brand = cleanBrandSuffixes(brand)

	// 基本空值检查
	brand = strings.TrimSpace(brand)
	if brand == "" {
		return false
	}

	// 移除医疗前缀限制 - 允许AI自由提取品牌
	// 这些词汇可能是品牌名称的一部分，不应该被过滤
	/*
		medicalPrefixes := []string{
			"医用", "无菌", "一次性", "灭菌", "消毒", "外科", "临床", "家用",
			"专业", "高级", "标准", "进口", "国产", "正品", "原装",
		}

		for _, prefix := range medicalPrefixes {
			if brand == prefix {
				return false
			}
		}
	*/

	// 放宽品牌名长度限制（1-50字符）
	if len(brand) < 1 || len(brand) > 50 {
		return false
	}

	// 只排除明确的无效标识
	invalidBrands := []string{
		"无品牌", "无", "未知",
	}

	brandLower := strings.ToLower(brand)
	for _, invalid := range invalidBrands {
		if strings.ToLower(invalid) == brandLower {
			return false
		}
	}

	// 只排除纯数字
	if matched, _ := regexp.MatchString(`^\d+$`, brand); matched {
		return false
	}

	// 只排除明显的纯规格信息（减少限制）
	specPatterns := []string{
		`^\d+\.?\d*\s*(?:ml|g|mg|cm|mm|支|片|粒|个|只|袋|包|瓶|盒|套)$`, // 纯规格如 "30ml", "50片"
		`^\d+\.?\d*\s*[xX*×]\s*\d+\.?\d*$`,                    // 纯尺寸如 "10x20"
	}

	for _, pattern := range specPatterns {
		if matched, _ := regexp.MatchString(pattern, brand); matched {
			return false
		}
	}

	return true
}

// 清理品牌名中的特殊后缀
func cleanBrandSuffixes(brand string) string {
	// 移除常见的商品描述后缀
	suffixPatterns := []string{
		`-新老包装随机发?`,
		`-新老随机`,
		`-随机发货`,
		`-包装随机`,
		`新老包装随机`,
		`新老随机`,
		`随机发货`,
		`包装随机`,
	}

	for _, pattern := range suffixPatterns {
		re := regexp.MustCompile(pattern)
		brand = re.ReplaceAllString(brand, "")
	}

	return strings.TrimSpace(brand)
}

func generateAlternativeTags(coreTag string, existingProducts []db.Product) []string {
	tagFrequency := make(map[string]int)

	for _, p := range existingProducts {
		if p.CoreTag != "" {
			tagFrequency[p.CoreTag]++
		}
	}

	options := []string{
		coreTag + " (热销)",
		coreTag + " (精选)",
	}

	for tag, count := range tagFrequency {
		if tag != coreTag && count > 3 && strings.HasPrefix(tag, coreTag) {
			options = append(options, tag)
		}
	}

	modifiers := []string{"专业", "高效", "便携", "升级"}
	for _, mod := range modifiers {
		options = append(options, fmt.Sprintf("%s%s", coreTag, mod))
	}

	if len(options) > 6 {
		return options[:6]
	}
	return options
}

func generatePrompt(p db.Product, samples []db.Product) string {
	var promptBuilder strings.Builder

	if len(samples) > 0 {
		promptBuilder.WriteString("## 相关商品参考：\n")
		addedTags := make(map[string]bool)

		for _, sample := range samples {
			if addedTags[sample.CoreTag] {
				continue
			}

			promptBuilder.WriteString(fmt.Sprintf("- %s → %s\n", sample.CoreTag, sample.ProductName))
			addedTags[sample.CoreTag] = true
		}
	}

	promptBuilder.WriteString(fmt.Sprintf("\n商品名称: %s\n", p.ProductName))
	promptBuilder.WriteString("\n请生成标签：")

	return promptBuilder.String()
}

// ====================== DeepSeekClient 方法 ======================

// 智能重试机制的请求发送
func (c *DeepSeekClient) sendRequestWithRetry(ctx context.Context, req deepSeekRequest, _ string) (string, error) {
	maxRetries := 3
	baseDelay := 1 * time.Second

	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避
			delay := time.Duration(float64(baseDelay) * math.Pow(2, float64(attempt-1)))
			fmt.Printf("🔄 第%d次重试，等待%v...\n", attempt, delay)

			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(delay):
			}

			// 只使用Chat模型，无需降级逻辑
		}

		// 为每次重试创建新的上下文，避免超时累积
		attemptCtx, cancel := context.WithTimeout(ctx, 90*time.Second)

		content, err := c.sendRequestInternal(attemptCtx, req)
		cancel()

		if err == nil {
			if attempt > 0 {
				fmt.Printf("✅ 重试成功！\n")
			}
			return content, nil
		}

		lastErr = err

		// 判断是否应该重试
		if !c.shouldRetry(err, attempt, maxRetries) {
			break
		}

		fmt.Printf("❌ 第%d次尝试失败: %v\n", attempt+1, err)
	}

	return "", fmt.Errorf("重试%d次后仍失败: %w", maxRetries, lastErr)
}

// shouldRetry 判断是否应该重试
func (c *DeepSeekClient) shouldRetry(err error, attempt, maxRetries int) bool {
	if attempt >= maxRetries {
		return false
	}

	errStr := err.Error()

	// 不重试的错误类型
	nonRetryableErrors := []string{
		"invalid_request_error",
		"authentication_error",
		"permission_error",
		"invalid_api_key",
		"quota_exceeded",
		"序列化请求失败",
		// 移除"解析响应失败"，因为这可能是由于超时导致的
	}

	for _, nonRetryable := range nonRetryableErrors {
		if strings.Contains(errStr, nonRetryable) {
			fmt.Printf("🚫 检测到不可重试错误: %s\n", nonRetryable)
			return false
		}
	}

	// 可重试的错误类型
	retryableErrors := []string{
		"timeout",
		"deadline exceeded",
		"connection reset",
		"connection refused",
		"temporary failure",
		"rate_limit_exceeded",
		"server_error",
		"bad_gateway",
		"service_unavailable",
		"gateway_timeout",

		"发送请求失败",
		"未收到有效的响应内容",
	}

	for _, retryable := range retryableErrors {
		if strings.Contains(errStr, retryable) {
			fmt.Printf("🔄 检测到可重试错误: %s\n", retryable)
			return true
		}
	}

	// 默认重试网络相关错误
	return strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "连接") ||
		strings.Contains(errStr, "超时")
}

// 内部请求发送实现
func (c *DeepSeekClient) sendRequestInternal(ctx context.Context, req deepSeekRequest) (string, error) {
	// 只使用普通请求
	return c.sendNormalRequest(ctx, req)
}

// 普通请求处理（Chat模型）
func (c *DeepSeekClient) sendNormalRequest(ctx context.Context, req deepSeekRequest) (string, error) {
	cfg := config.GetConfig()

	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("%w: %v", ErrMarshalRequestBody, err)
	}

	// 使用更健壮的HTTP客户端
	client := &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
		Timeout: 120 * time.Second, // 增加超时时间到120秒
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", deepSeekAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("%w: %v", ErrCreateRequest, err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	// 默认配置值
	maxRetries := 3
	initialBackoff := 1.0 // 默认1秒

	// 动态获取配置值（带默认值）
	if cfg != nil {
		// 使用反射安全地获取配置字段
		configValue := reflect.ValueOf(cfg).Elem()

		// 获取ApiOptimization字段
		apiOptField := configValue.FieldByName("ApiOptimization")
		if apiOptField.IsValid() {
			apiOpt := apiOptField.Interface()
			apiOptValue := reflect.ValueOf(apiOpt)

			// 获取MaxRetries
			maxRetriesField := apiOptValue.FieldByName("MaxRetries")
			if maxRetriesField.IsValid() && maxRetriesField.Kind() == reflect.Int {
				maxRetries = int(maxRetriesField.Int())
			}

			// 获取InitialBackoff
			backoffField := apiOptValue.FieldByName("InitialBackoff")
			if backoffField.IsValid() && backoffField.Kind() == reflect.Float64 {
				initialBackoff = backoffField.Float()
			}
		}
	}

	for attempt := 0; attempt < maxRetries; attempt++ {
		// 创建带超时的上下文（每次重试增加超时）
		attemptCtx, cancelAttempt := context.WithTimeout(ctx, time.Duration(attempt+1)*60*time.Second)
		defer cancelAttempt()

		reqWithCtx := httpReq.Clone(attemptCtx)

		resp, err := client.Do(reqWithCtx)
		if err != nil {
			if attempt < maxRetries-1 {
				backoff := time.Duration(1<<uint(attempt)) * time.Duration(initialBackoff*float64(time.Second))
				fmt.Printf("[DeepSeek API] %s: %v, 将在 %v 后重试\n", ErrSendRequest, err, backoff)
				time.Sleep(backoff)
				continue
			}
			return "", fmt.Errorf("%w: %v", ErrSendRequest, err)
		}

		if resp.StatusCode == http.StatusOK {
			defer resp.Body.Close()

			// 增强的响应体读取逻辑
			var body []byte
			var err error
			const maxRetryRead = 2

			for i := range maxRetryRead {
				body, err = io.ReadAll(resp.Body)
				if err == nil {
					break
				}

				// 检查是否是解压缩错误
				if strings.Contains(err.Error(), "compress/flate: corrupt input") {
					fmt.Printf("! 解压缩错误 (尝试 %d/%d): %v\n", i+1, maxRetryRead, err)
					if i < maxRetryRead-1 {
						time.Sleep(500 * time.Millisecond)
					}
					continue
				}
				break
			}

			if err != nil {
				return "", fmt.Errorf("%w: %v", ErrReadResponse, err)
			}

			// 响应数据完整性检查
			if len(body) < 10 {
				return "", fmt.Errorf("%w: 响应体过短 (%d bytes)", ErrDataCorruption, len(body))
			}

			// 调试模式下记录部分响应数据
			if os.Getenv("DEBUG_AI_RESPONSE") == "true" {
				sampleSize := min(512, len(body))
				fmt.Printf("[DEBUG] 响应样本 (%d bytes):\n%.512s...\n", len(body), string(body[:sampleSize]))
			}

			var response deepSeekResponse
			if err := json.Unmarshal(body, &response); err != nil {
				// 尝试修复常见的JSON格式错误
				if fixedBody, ok := fixJSONErrors(body); ok {
					if err := json.Unmarshal(fixedBody, &response); err == nil {
						fmt.Println("✓ 已修复JSON格式错误")
						goto validResponse
					}
				}
				return "", fmt.Errorf("%w: %v", ErrUnmarshalResponse, err)
			}

		validResponse:
			if len(response.Choices) == 0 {
				return "", ErrNoChoicesGenerated
			}

			content := response.Choices[0].Message.Content

			// 普通请求（Chat模型）直接返回内容
			return content, nil
		}

		resp.Body.Close()

		// 处理限流和服务器错误
		if resp.StatusCode == http.StatusTooManyRequests {
			if attempt < maxRetries-1 {
				backoff := time.Duration(1<<uint(attempt)) * time.Duration(initialBackoff*float64(time.Second))
				fmt.Printf("[DeepSeek API] 频率限制，将在 %v 后重试\n", backoff)
				time.Sleep(backoff)
				continue
			}
			return "", ErrRateLimitExceeded
		}

		if resp.StatusCode >= 500 && resp.StatusCode < 600 && attempt < maxRetries-1 {
			backoff := time.Duration(1<<uint(attempt)) * time.Duration(initialBackoff*float64(time.Second))
			fmt.Printf("[DeepSeek API] 服务器错误，将在 %v 后重试\n", backoff)
			time.Sleep(backoff)
			continue
		}

		return "", fmt.Errorf("API请求失败 (状态码 %d)", resp.StatusCode)
	}

	return "", ErrMaxRetriesExceeded
}

// fixJSONErrors 尝试修复常见的JSON格式错误
func fixJSONErrors(data []byte) ([]byte, bool) {
	// 尝试修复常见的JSON格式错误
	patterns := []struct {
		pattern     string
		replacement string
	}{
		{`([,:{])\s*([}\]])`, `$1 $2`},                          // 修复缺少空格
		{`([a-zA-Z0-9_"]+)\s*:\s*([a-zA-Z0-9_"]+)`, `"$1": $2`}, // 修复缺少引号的键
		{`\\u0000`, ``},     // 移除空字符
		{`[\x00-\x1F]`, ``}, // 移除控制字符
	}

	fixed := string(data)
	for _, p := range patterns {
		re := regexp.MustCompile(p.pattern)
		fixed = re.ReplaceAllString(fixed, p.replacement)
	}

	// 尝试解析修复后的JSON
	if json.Valid([]byte(fixed)) {
		return []byte(fixed), true
	}
	return nil, false
}

func (c *DeepSeekClient) GenerateTags(ctx context.Context, product db.Product, samples []db.Product) (db.Product, []string, error) {
	products := []db.Product{product}
	resultProducts, options, errs := c.BatchGenerateTags(ctx, products, samples)
	if len(errs) > 0 && errs[0] != nil {
		return product, nil, errs[0]
	}

	// 返回三个值
	return resultProducts[0], options[0], nil
}

func (c *DeepSeekClient) BatchGenerateTags(ctx context.Context, products []db.Product, samples []db.Product) ([]db.Product, [][]string, []error) {
	// 简化监控统计
	var funnelStats struct {
		total          int
		brandExtracted int
		aiProcessed    int
		jsonParsed     int
		finalSuccess   int
		brandMissing   int
		aiFailure      int
		jsonFailure    int
		mu             sync.Mutex
	}
	funnelStats.total = len(products)

	// 添加总体超时控制
	ctx, cancel := context.WithTimeout(ctx, 15*time.Minute)
	defer cancel()

	startTime := time.Now()

	if c.apiKey == "" {
		errs := make([]error, len(products))
		for i := range errs {
			errs[i] = ErrNoAPIKey
		}
		fmt.Println("!!! 错误: DeepSeek API密钥未配置，无法处理AI请求")
		fmt.Println("!!! 请检查配置文件中的 deepseek_api_key 设置")
		fmt.Printf("!!! 配置文件路径: %s\n", config.ConfigPath)
		return products, make([][]string, len(products)), errs
	}

	cfg := config.GetConfig()
	maxConcurrency := 3 // 降低并发数以提高稳定性

	// 安全获取最大并发数配置
	if cfg != nil {
		// 使用反射获取ApiOptimization字段
		configValue := reflect.ValueOf(cfg).Elem()
		apiOptField := configValue.FieldByName("ApiOptimization")
		if apiOptField.IsValid() {
			apiOpt := apiOptField.Interface()
			apiOptValue := reflect.ValueOf(apiOpt)

			// 获取MaxConcurrency
			concurrencyField := apiOptValue.FieldByName("MaxConcurrency")
			if concurrencyField.IsValid() && concurrencyField.Kind() == reflect.Int {
				concurrency := int(concurrencyField.Int())
				if concurrency > 0 {
					maxConcurrency = concurrency
				}
			}
		}
	}

	// 结果收集
	type result struct {
		index   int
		product db.Product
		options []string
		err     error
	}

	results := make(chan result, len(products))
	sem := make(chan struct{}, maxConcurrency) // 并发控制信号量
	var wg sync.WaitGroup

	// 为每个商品启动处理任务
	for i := range products {
		wg.Add(1)
		go func(idx int, p db.Product) {
			defer wg.Done()
			defer func() {
				// === 增强监控：Panic恢复 ===
				if r := recover(); r != nil {
					fmt.Printf("\n🚨 [PANIC监控] 商品%d处理发生panic: %v\n", idx+1, r)
					fmt.Printf("[PANIC监控] 产品名称: '%s'\n", p.ProductName)
					fmt.Printf("[PANIC监控] 原始品牌: '%s'\n", p.Brand)
					fmt.Printf("[PANIC监控] 返回原始商品数据\n")
					results <- result{
						index:   idx,
						product: p, // 返回原始商品，保留原始品牌
						options: []string{},
						err:     fmt.Errorf("处理发生panic: %v", r),
					}
				}
			}()

			sem <- struct{}{}
			defer func() {
				<-sem
				// 确保在内存不足时释放资源
				if mem := getMemoryUsage(); mem > 80 {
					runtime.GC()
				}
			}()

			// === 增强监控：上下文检查 ===
			select {
			case <-ctx.Done():
				fmt.Printf("\n⏰ [超时监控] 商品%d处理超时\n", idx+1)
				fmt.Printf("[超时监控] 产品名称: '%s'\n", p.ProductName)
				fmt.Printf("[超时监控] 原始品牌: '%s'\n", p.Brand)
				results <- result{
					index:   idx,
					product: p, // 返回原始商品，保留原始品牌
					options: []string{},
					err:     ctx.Err(),
				}
				return
			default:
			}

			// 统计初始品牌状态
			initialBrand := p.Brand
			if initialBrand != "" && initialBrand != "无品牌" {
				funnelStats.mu.Lock()
				funnelStats.brandExtracted++
				funnelStats.mu.Unlock()
			} else {
				funnelStats.mu.Lock()
				funnelStats.brandMissing++
				funnelStats.mu.Unlock()
			}

			// 处理单个商品
			reqCtx, cancel := context.WithTimeout(ctx, 90*time.Second)
			defer cancel()

			processed, options, err := c.processSingleProduct(reqCtx, p, samples)

			// === 🔧 强化品牌修复机制 ===
			if err != nil {
				fmt.Printf("[品牌修复-并发-%d] ❌ AI处理失败: %v\n", idx+1, err)
				// AI处理失败时，使用规则提取品牌
				processed = p // 保持原始产品数据
				extractedBrand := db.ExtractBrand(p.ProductName, []string{"正品", "专供", "旗舰店"})
				if extractedBrand != "" && extractedBrand != "无品牌" {
					processed.Brand = extractedBrand
					fmt.Printf("[品牌修复-并发-%d] 🔧 规则提取品牌成功: '%s'\n", idx+1, extractedBrand)
				} else {
					fmt.Printf("[品牌修复-并发-%d] ❌ 规则提取品牌失败，保持空值\n", idx+1)
				}
				options = []string{}
			} else {
				// AI处理成功，但仍需检查品牌
				if processed.Brand == "" || processed.Brand == "无品牌" {
					fmt.Printf("[品牌修复-并发-%d] ⚠️ AI处理成功但品牌为空，尝试规则提取\n", idx+1)
					extractedBrand := db.ExtractBrand(p.ProductName, []string{"正品", "专供", "旗舰店"})
					if extractedBrand != "" && extractedBrand != "无品牌" {
						processed.Brand = extractedBrand
						fmt.Printf("[品牌修复-并发-%d] 🔧 规则提取品牌成功: '%s'\n", idx+1, extractedBrand)
					}
				} else {
					fmt.Printf("[品牌修复-并发-%d] ✅ AI品牌有效: '%s'\n", idx+1, processed.Brand)
				}
			}

			// 统计处理结果
			if err != nil {
				funnelStats.mu.Lock()
				funnelStats.aiFailure++
				funnelStats.mu.Unlock()
			} else {
				funnelStats.mu.Lock()
				funnelStats.aiProcessed++
				funnelStats.mu.Unlock()

				// JSON解析检查
				if processed.CoreTag != "" {
					funnelStats.mu.Lock()
					funnelStats.jsonParsed++
					funnelStats.mu.Unlock()
				} else {
					funnelStats.mu.Lock()
					funnelStats.jsonFailure++
					funnelStats.mu.Unlock()
				}

				// 最终品牌检查
				if processed.Brand != "" && processed.Brand != "无品牌" {
					funnelStats.mu.Lock()
					funnelStats.finalSuccess++
					funnelStats.mu.Unlock()
				}
			}

			results <- result{
				index:   idx,
				product: processed,
				options: options,
				err:     err,
			}
		}(i, products[i])
	}

	// 等待所有任务完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	resultProducts := make([]db.Product, len(products))
	allOptions := make([][]string, len(products))
	allErrors := make([]error, len(products))

	for res := range results {
		resultProducts[res.index] = res.product
		allOptions[res.index] = res.options
		allErrors[res.index] = res.err
	}

	aiDuration := time.Since(startTime)
	fmt.Printf("[DeepSeek API] AI处理完成，总耗时: %v, 平均 %.2f秒/商品\n",
		aiDuration, aiDuration.Seconds()/float64(len(products)))

	// 使用新的正确流程进行同批处理
	fmt.Printf("[DeepSeek API] 开始使用正确流程进行同批处理...\n")
	unificationStart := time.Now()

	unifiedProducts := c.ApplyCorrectBatchProcessing(resultProducts)

	// 应用规则引擎进行后处理（包括核心标签修正）
	fmt.Printf("[DeepSeek API] 开始应用规则引擎后处理...\n")
	ruleEngineStart := time.Now()

	ruleEngine := RuleEngine()
	for i := range unifiedProducts {
		err := ruleEngine.ApplyRules(&unifiedProducts[i])
		if err != nil {
			fmt.Printf("[DeepSeek API] 规则引擎处理商品%d失败: %v\n", i+1, err)
		}
	}

	ruleEngineDuration := time.Since(ruleEngineStart)
	unificationDuration := time.Since(unificationStart)
	totalDuration := time.Since(startTime)

	fmt.Printf("[DeepSeek API] 规则引擎后处理完成，耗时: %v\n", ruleEngineDuration)
	fmt.Printf("[DeepSeek API] 正确流程处理完成，总耗时: %v\n", unificationDuration)
	fmt.Printf("[DeepSeek API] 总处理时间: %v (AI: %v + 统一: %v + 规则: %v)\n",
		totalDuration, aiDuration, unificationDuration-ruleEngineDuration, ruleEngineDuration)

	// 简化统计报告 - 仅在有错误时显示
	if funnelStats.aiFailure > 0 || funnelStats.jsonFailure > 0 {
		fmt.Printf("⚠️ 处理异常: AI失败%d个, JSON失败%d个\n",
			funnelStats.aiFailure, funnelStats.jsonFailure)
	}

	return unifiedProducts, allOptions, allErrors
}

// getMemoryUsage 获取当前内存使用百分比
func getMemoryUsage() float64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	allocMB := float64(m.Alloc) / 1024 / 1024
	sysMB := float64(m.Sys) / 1024 / 1024
	return (allocMB / sysMB) * 100
}

func (c *DeepSeekClient) processSingleProduct(ctx context.Context, product db.Product, samples []db.Product) (db.Product, []string, error) {
	// 错误恢复机制
	defer func() {
		if r := recover(); r != nil {
			panic(r)
		}
	}()

	// 强化系统提示词（包含特征标签）
	systemPrompt := `你是美团的医疗器械前置仓运营助手，请严格按照以下规则为商品生成标准化标签：

    # 标签生成规则（按Excel列顺序）
    1. B列-核心标签(core_tag):
       - 严格限制2-4个字，不能超过4个字，提取表达商品本质的名词，优先保留功能特征
       - 层次化提取规则（按优先级）:
         a) 功能特征+主体: "痘肌型凝胶" -> "痘肌凝胶"
         b) 用途特征+主体: "唇部型凝胶" -> "唇部凝胶"
         c) 材质特征+主体: "胶原蛋白凝胶" -> "胶原蛋白凝胶"
         d) 成分特征+主体: "酒精湿巾" -> "酒精湿巾"
         e) 通用主体: "医用凝胶" -> "凝胶"
       - 示例:
            "红富士苹果" -> "苹果"
            "痘肌型医用退热凝胶" -> "痘肌凝胶"
            "酒精卫生湿巾" -> "酒精湿巾"
            "耳塞类产品" -> "耳塞"（固定规则）
            "胶原蛋白精华液" -> "精华液"
            "虾青素去痘精华液" -> "精华液"
    
    2. C列-场景标签(scene): 
       - 单选，根据商品用途选择，但注意这些不是类目名称：
         • 医疗器械类: 术后康复 | 慢病管理 | 专业防护 | 急救场景 | 疼痛缓解 | 
                       创面护理 | 血糖监测 | 呼吸支持 | 康复训练 | 中医理疗
         • 非医疗器械类: 日常保健 | 家庭清洁 | 便捷生活 | 旅行备用 | 两性健康 | 
                       运动防护 | 能量补充 | 睡眠改善 | 口腔护理 | 肌肤保养
         • 日常用品类: 家居整理 | 餐饮准备 | 日常消耗 | 卫浴清洁 | 收纳管理 | 
                     洗涤护理 | 厨房清洁 | 个人卫生
    
    3. D列-人群标签(crowd): 
       - 单选: 女性 | 儿童 | 男性 | 婴儿 | 孕妇 | 成人
    

    4. F列-规格字段(spec):
       - 提取商品规格信息，优先提取最完整的规格
       - 优先级顺序：数量+包装 > 容量+包装 > 尺寸规格 > 单一数量 > 单一容量
       - 示例:
            "[素秀臣氏]早C晚A金胶精华保湿滋润扶纹抗皱500mg30粒_罐" -> "30粒"
            "[雅貌]新老包装 医用540一次性滚针美容院生发微针脸部滚轮手动精华导入生发痘印章" -> "540针"
            "T杏仁酸精华30mI油痘肌控油祛痘淡化痘印去闭口收缩毛孔提亮" -> "30ml"
            "T【形象美】虾青素去痘精华液控油去痘柔滑嫩肤细致毛孔亮肤补水原液30ml_瓶" -> "30ml"
            "华西医用棉签采耳棉签耳鼻喉专用无菌棉签15cm20支超细小棉签耳签棉棒_包" -> "20支"
            "云南白药牙膏180g" -> "180g"
    
    5. H列-二级类目(category2):
       - 1-2个，严格遵守从以下列表选择:
       ` + getCategoryList() + `
    
    6. J列-特征标签(feature_tags):
       - 1个，提取商品最显著的特征属性
       - 示例: "防水创可贴" -> ["防水"]
    
    # 特殊规则（必须遵守）
    - 暖宝宝产品:
         • 核心标签 -> "暖宝宝"
         • 二级类目 -> ["热敷/暖宫"]（固定）
    - 耳塞类产品:
         • 核心标签 -> "耳塞"（固定）
         • 二级类目 -> ["睡眠改善"]（固定）
    - 酒精棉/防水贴类产品:
         • 商品名包含"棉球"、"防水贴"、"酒精棉"、"酒精棉球"、"医用棉球"、"棉垫"、"夹棉"等
         • 二级类目 -> ["酒精棉/防水贴"]（固定）
    - 纱布类产品统一规则:
         • 商品名包含"纱布胶带"、"纱布绷带"、"纱布片"、"纱布块"等
         • 核心标签 -> "纱布"（固定统一）
         • 二级类目 -> ["纱布/胶带"]（固定）
    - 凝胶类产品细分规则:
         • 商品名包含"痘肌"、"祛痘"、"痘痘"等 -> 核心标签: "痘肌凝胶"
         • 商品名包含"唇部"、"唇裂"、"润唇"等 -> 核心标签: "唇部凝胶"
         • 商品名包含"修复"、"烧烫伤"、"胶原蛋白"等 -> 核心标签: "修复凝胶"
         • 商品名包含"退热"但无其他特征 -> 核心标签: "退热凝胶"
    - 湿巾类产品细分规则（强制保留"湿巾"主体词）:
         • 商品名包含"湿巾"或"湿纸巾"且包含"酒精"、"消毒" -> 核心标签: "酒精湿巾"（固定）
         • 商品名包含"湿巾"或"湿纸巾"且包含"婴儿"、"手口" -> 核心标签: "婴儿湿巾"（固定）
         • 商品名包含"湿巾"或"湿纸巾"但无其他特征 -> 核心标签: "湿巾"（固定）
         • 注意：湿巾类商品必须保留"湿巾"作为主体词，不能只提取特征词
    - 禁用规则:
         • 孕妇禁用产品: 不能分到"宝妈用品"类目
         • 儿童禁用产品: 不能分到"儿童保健"、"婴儿用品"、"儿童健康"类目
         • 婴儿禁用产品: 不能分到"婴儿用品"、"儿童保健"类目
    - 特殊人群禁用商品: 根据实际功能正常分类
    
    # 输出要求
    1. 严格使用JSON格式，字段顺序和含义必须与表格列对应:
       {
         "core_tag": "",    // B列-核心标签
         "scene": "",       // C列-场景标签
         "crowd": "",       // D列-人群标签
         "brand": "",       // E列-品牌字段（必填）
         "spec": "",        // F列-规格字段
         "category2": [],   // H列-二级类目（数组）
         "feature_tags": [""] // J列-特征标签（单元素数组）
       }
    2. 所有字段值必须使用简体中文
    3. 不要返回商品名称(A列)和品名清洗(I列)相关内容
    4. 不要返回任何额外字段或说明文字
    5. 严格遵循特殊规则，否则会造成类目分配错误`

	userPrompt := generatePrompt(product, samples)

	medConfig := GetMedicalGoodsConfig()

	// 使用Chat模型
	currentModel := c.getCurrentModel()

	requestBody := deepSeekRequest{
		Model: currentModel,
		Messages: []message{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		Temperature:       medConfig.Temperature,
		TopP:              medConfig.TopP,
		MaxTokens:         medConfig.MaxTokens,
		RepetitionPenalty: medConfig.RepetitionPenalty,
		ResponseFormat:    responseFormat{Type: "json_object"},
		ExtraOptions:      extraOptions{IncludeThoughts: false},
	}

	// 品牌提取
	if product.Brand == "" {
		extractedBrand := ExtractBrand(product.ProductName)
		if extractedBrand != "" {
			normalizedBrand := NormalizeBrand(extractedBrand)
			product.Brand = utils.EnsureUTF8(normalizedBrand)
		} else {
			product.Brand = "无品牌"
		}
	}

	// 检查上下文
	select {
	case <-ctx.Done():
		return product, nil, ctx.Err()
	default:
	}

	// AI处理
	content, err := c.sendRequestWithRetry(ctx, requestBody, product.ProductName)
	if err != nil {
		return product, nil, fmt.Errorf("AI处理失败: %w", err)
	}

	tags, err := parseSingleResponse(content)
	if err != nil {
		return product, nil, fmt.Errorf("解析响应失败: %w", err)
	}

	processedProduct, options := processTags(product, tags, samples)

	return processedProduct, options, nil
}

func getCategoryList() string {
	categories := []string{
		"女性垫巾", "纱布/胶带", "家庭/护理", "皮肤消毒", "防护口罩",
		"绷带/固定带", "创可贴/创口贴", "敷贴/敷料", "酒精棉/防水贴",
		"湿巾/纸巾", "一次性辅助护理", "一次性居家日用",
		"测孕排卵试纸", "流感病毒检测", "艾滋/TP检测", "hpv检测", "其他检测",
		"痘痘护理", "疤痕护理", "皮肤修复",
		"彩色美瞳", "透明隐形", "护理液", "眼镜配件",
		"胸背护具", "颈部护具", "腰部护具", "腿部护具", "手部护具", "医用防护",
		"鼻腔护理", "眼部护理", "口腔护理", "耳部护理", "咽喉护理",
		"足部护理", "头皮护理", "脱毛/祛味", "肛周护理", "睡眠改善",
		"医用护肤", "面部护理", "身体护理", "眼部保养", "防晒止汗", "面部清洁", "彩妆护理", "美容仪器", "唇部护理",
		"保健食品", "维生素", "养生茶饮", "营养补充",
		"理疗器械", "按摩精油", "热敷/暖宫", "煎药器材", "理疗贴", "艾灸/针灸",
		"退热用品", "体温测量",
		"制氧", "雾化", "呼吸",
		"皮肤护理", "手足护理", "驱蚊消痒", "私处护理",
		"活血化瘀", "止疼膏药", "晕车贴", "辅助理疗贴",
		"止咳贴", "儿童保健", "婴儿用品",
		"宝妈用品", "私处保养", "胸部护理",
		"男性护理",
		"避孕套", "润滑液/喷剂", "玩具类", "抗HPV",
		"小家电", "生活日用", "沐浴露/润肤乳", "洗发护发",
		"轮椅", "腋拐肘拐", "坐便辅助",
		"血糖监测", "胰岛素管理", "血氧监测", "血压监测", "胎心监测", "其他监测",
		"钳剪镊刀", "急救设备", "康复训练",
		"造口护理", "肠胃护理", "泌尿护理", "康复辅助",
	}
	return strings.Join(categories, " | ")
}

// ApplyBatchUnification 应用同批核心标签统一逻辑（公开方法）
func (c *DeepSeekClient) ApplyBatchUnification(products []db.Product) []db.Product {
	if len(products) <= 1 {
		return products
	}

	// 简化版本的同批统一逻辑
	unifiedProducts := c.simpleBatchUnification(products)

	// 统计统一效果
	changeCount := 0
	for i, unified := range unifiedProducts {
		if unified.CoreTag != products[i].CoreTag {
			changeCount++
			fmt.Printf("[统一] %s -> %s\n", products[i].CoreTag, unified.CoreTag)
		}
	}

	if changeCount > 0 {
		fmt.Printf("[DeepSeek API] 同批统一: %d/%d 个商品标签被统一\n", changeCount, len(products))
	} else {
		fmt.Printf("[DeepSeek API] 同批统一: 无需统一，所有标签已一致\n")
	}

	return unifiedProducts
}

// simpleBatchUnification 简化版本的同批统一逻辑
func (c *DeepSeekClient) simpleBatchUnification(products []db.Product) []db.Product {
	if len(products) <= 1 {
		return products
	}

	// 创建标签频率统计
	tagFreq := make(map[string][]int) // 标签 -> 商品索引列表
	for i, product := range products {
		tag := strings.TrimSpace(product.CoreTag)
		if tag != "" {
			tagFreq[tag] = append(tagFreq[tag], i)
		}
	}

	// 查找相似标签并统一
	result := make([]db.Product, len(products))
	copy(result, products)

	// 预定义的统一规则
	unificationRules := map[string]string{
		"生理性盐水":  "生理盐水",
		"氯化钠注射液": "生理盐水",
		"氯化钠溶液":  "生理盐水",
		"精华":     "精华液",
		"瘢痕贴":    "疤痕贴",
		"疤痕敷贴":   "疤痕贴",
		"瘢痕敷贴":   "疤痕贴",
		"坐便器":    "坐便椅",
		"坐便凳":    "坐便椅",
		"大便椅":    "坐便椅",
		"棉棒":     "医用棉签",
		"碘伏棉棒":   "碘伏棉签",
	}

	// 应用统一规则
	for i, product := range result {
		if unifiedTag, exists := unificationRules[product.CoreTag]; exists {
			result[i].CoreTag = unifiedTag
		}
	}

	// 重新统计标签频率（应用规则后）
	tagFreq = make(map[string][]int)
	for i, product := range result {
		tag := strings.TrimSpace(product.CoreTag)
		if tag != "" {
			tagFreq[tag] = append(tagFreq[tag], i)
		}
	}

	// 基于相似度的统一（简化版本）
	for tag1, indices1 := range tagFreq {
		if len(indices1) == 0 {
			continue
		}

		for tag2, indices2 := range tagFreq {
			if tag1 >= tag2 || len(indices2) == 0 { // 避免重复比较
				continue
			}

			// 简单的相似度检查
			if c.AreTagsSimilar(tag1, tag2) {
				// 选择更常见的标签作为统一标签
				targetTag := tag1
				sourceIndices := indices2
				if len(indices2) > len(indices1) {
					targetTag = tag2
					sourceIndices = indices1
				}

				// 统一标签
				for _, idx := range sourceIndices {
					result[idx].CoreTag = targetTag
				}

				// 清空已处理的标签
				tagFreq[tag2] = []int{}
			}
		}
	}

	return result
}

// ExtractBrandWithAI 使用AI专门提取品牌信息
func ExtractBrandWithAI(productName string) string {
	fmt.Printf("[ExtractBrandWithAI] 🤖 开始AI品牌提取: '%s'\n", productName)

	// 创建AI客户端
	client := NewDeepSeekClient()
	if client.apiKey == "" {
		fmt.Printf("[ExtractBrandWithAI] ❌ API密钥未配置\n")
		return ""
	}

	// 构建简化的品牌提取提示词（让AI自由发挥）
	systemPrompt := `请从商品名称中提取品牌名称。

例如：袋鼠医生创可贴防水透气创口贴100片装混合装 → 袋鼠医生

如果无法识别出明确的品牌，请返回"无品牌"。

只返回品牌名称，不要添加任何解释。`

	userPrompt := fmt.Sprintf("商品名称: %s\n请提取品牌:", productName)

	// 使用标准的deepSeekRequest结构以支持重试机制
	selectedModel := client.getCurrentModel()
	fmt.Printf("[ExtractBrandWithAI] 🔧 使用模型: %s\n", selectedModel)

	requestBody := deepSeekRequest{
		Model: selectedModel,
		Messages: []message{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		Temperature:    0.1,
		MaxTokens:      50,
		ResponseFormat: responseFormat{Type: "text"}, // 品牌提取使用文本格式
	}

	// 发送请求 - 增加超时时间到120秒
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	// 添加调试信息
	fmt.Printf("[ExtractBrandWithAI] 🔧 请求详情: Model=%s, Temperature=%.1f\n", requestBody.Model, requestBody.Temperature)
	fmt.Printf("[ExtractBrandWithAI] 🔧 系统提示词长度: %d 字符\n", len(systemPrompt))
	fmt.Printf("[ExtractBrandWithAI] 🔧 用户提示词: %s\n", userPrompt)

	// 使用带重试机制的请求发送
	response, err := client.sendRequestWithRetry(ctx, requestBody, "brand-extraction")
	if err != nil {
		fmt.Printf("[ExtractBrandWithAI] ❌ AI请求失败: %v\n", err)
		fmt.Printf("[ExtractBrandWithAI] 🔧 请求体: %+v\n", requestBody)

		// 回退机制：使用规则匹配提取品牌
		fmt.Printf("[ExtractBrandWithAI] 🔄 启用回退机制：规则匹配品牌提取\n")
		fallbackBrand := extractBrandByRules(productName)
		if fallbackBrand != "" && fallbackBrand != "无品牌" {
			fmt.Printf("[ExtractBrandWithAI] ✅ 规则匹配成功: %s\n", fallbackBrand)
			return fallbackBrand
		}

		return ""
	}

	// 清理响应
	brand := strings.TrimSpace(response)
	brand = strings.Trim(brand, "\"'`")

	fmt.Printf("[ExtractBrandWithAI] 🎯 AI提取结果: '%s'\n", brand)

	// 验证结果
	if brand == "" || brand == "无品牌" || brand == "未知品牌" || brand == "无" {
		fmt.Printf("[ExtractBrandWithAI] ℹ️ AI未识别到有效品牌\n")
		return ""
	}

	// 长度验证
	if len(brand) > 30 {
		fmt.Printf("[ExtractBrandWithAI] ⚠️ 品牌名称过长，可能识别错误: '%s'\n", brand)
		return ""
	}

	fmt.Printf("[ExtractBrandWithAI] ✅ AI品牌提取成功: '%s'\n", brand)
	return brand
}

// extractBrandByRules 使用简化规则提取品牌（移除复杂正则，让AI自由发挥）
func extractBrandByRules(productName string) string {
	// 只保留最基本的括号提取，其他交给AI处理
	bracketPatterns := []string{
		`\[([^\]]+)\]`, // [品牌名]
		`【([^】]+)】`,    // 【品牌名】
		`\{([^}]+)\}`,  // {品牌名}
	}

	for _, pattern := range bracketPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(productName); len(matches) >= 2 {
			extracted := strings.TrimSpace(matches[1])
			if extracted != "" && len(extracted) <= 20 {
				return extracted
			}
		}
	}

	// 其他情况交给AI处理，不使用复杂的规则匹配

	return ""
}

// AreTagsSimilar 检查两个标签是否相似（使用统一的关键词提取算法）
func (c *DeepSeekClient) AreTagsSimilar(tag1, tag2 string) bool {
	// 去除空格
	tag1 = strings.TrimSpace(tag1)
	tag2 = strings.TrimSpace(tag2)

	if tag1 == tag2 {
		return true
	}

	// 使用统一的关键词提取算法进行相似度计算
	similarity := calculateTagSimilarity(tag1, tag2)

	// 如果相似度超过阈值，认为相似
	return similarity >= 0.85
}

// 标签相似度计算（基于关键词提取）
func calculateTagSimilarity(tag1, tag2 string) float64 {
	// 提取关键词
	keywords1 := extractTagKeywords(tag1)
	keywords2 := extractTagKeywords(tag2)

	if len(keywords1) == 0 || len(keywords2) == 0 {
		return 0.0
	}

	// 计算关键词重叠度
	commonKeywords := 0
	for _, k1 := range keywords1 {
		for _, k2 := range keywords2 {
			if k1 == k2 || isTagKeywordSimilar(k1, k2) {
				commonKeywords++
				break
			}
		}
	}

	maxKeywords := max(len(keywords1), len(keywords2))

	return float64(commonKeywords) / float64(maxKeywords)
}

// 提取标签关键词
func extractTagKeywords(tag string) []string {
	// 医疗用品关键词
	keywords := []string{
		"生理盐水", "生理性盐水", "氯化钠", "盐水", "注射液",
		"精华液", "精华", "原液", "精华素", "精华水",
		"疤痕贴", "瘢痕贴", "疤痕敷贴", "瘢痕敷贴", "疤痕修复贴",
		"坐便椅", "坐便器", "坐便凳", "大便椅", "厕所椅", "马桶椅",
		"医用棉签", "棉棒", "棉签", "碘伏棉棒", "碘伏棉签",
		"创可贴", "创口贴", "止血贴", "创伤贴",
		"消毒液", "消毒剂", "酒精", "碘伏",
		"口罩", "防护口罩", "医用口罩", "N95",
	}

	var found []string
	for _, keyword := range keywords {
		if strings.Contains(tag, keyword) {
			found = append(found, keyword)
		}
	}

	// 如果没有找到关键词，将整个标签作为关键词
	if len(found) == 0 {
		found = append(found, tag)
	}

	return found
}

// 检查标签关键词是否相似
func isTagKeywordSimilar(k1, k2 string) bool {
	similarPairs := [][]string{
		{"生理盐水", "生理性盐水"},
		{"精华液", "精华"},
		{"疤痕贴", "瘢痕贴"},
		{"疤痕敷贴", "瘢痕敷贴"},
		{"坐便椅", "坐便器"},
		{"坐便椅", "坐便凳"},
		{"医用棉签", "棉棒"},
		{"创可贴", "创口贴"},
	}

	for _, pair := range similarPairs {
		if (k1 == pair[0] && k2 == pair[1]) || (k1 == pair[1] && k2 == pair[0]) {
			return true
		}
	}

	return false
}

// ApplyCorrectBatchProcessing 使用正确流程进行批量处理
func (c *DeepSeekClient) ApplyCorrectBatchProcessing(products []db.Product) []db.Product {
	if len(products) <= 1 {
		return products
	}

	fmt.Printf("[正确流程] 开始处理 %d 个商品\n", len(products))

	// 创建全局的正确流程处理器
	processor := NewCorrectCoreTagProcessorForAI(c)

	// 逐个处理商品，利用渐进式学习
	processedProducts := make([]db.Product, len(products))
	changeCount := 0

	for i, product := range products {
		// 如果商品已经有核心标签，使用正确流程重新处理以确保统一
		originalTag := product.CoreTag

		coreTag, err := processor.ProcessProduct(product.ProductName)
		if err != nil {
			fmt.Printf("[正确流程] 商品 %d 处理失败: %v\n", i+1, err)
			processedProducts[i] = product // 保持原样
			continue
		}

		// 更新商品
		processedProducts[i] = product
		processedProducts[i].CoreTag = coreTag

		if originalTag != coreTag {
			changeCount++
			fmt.Printf("[正确流程] 商品 %d: %s -> %s\n", i+1, originalTag, coreTag)
		}
	}

	if changeCount > 0 {
		fmt.Printf("[正确流程] 批量处理: %d/%d 个商品标签被统一\n", changeCount, len(products))
	} else {
		fmt.Printf("[正确流程] 批量处理: 无需统一，所有标签已一致\n")
	}

	return processedProducts
}

// NewCorrectCoreTagProcessorForAI 为AI客户端创建正确流程处理器
func NewCorrectCoreTagProcessorForAI(aiClient *DeepSeekClient) *CorrectCoreTagProcessorAI {
	return &CorrectCoreTagProcessorAI{
		coreTagCache: make(map[string]string),
		aiClient:     aiClient,
		threshold:    0.85,
	}
}

// CorrectCoreTagProcessorAI AI客户端专用的正确流程处理器
type CorrectCoreTagProcessorAI struct {
	coreTagCache map[string]string
	aiClient     *DeepSeekClient
	threshold    float64
	mutex        sync.RWMutex
}

// ProcessProduct 处理单个商品
func (cctp *CorrectCoreTagProcessorAI) ProcessProduct(productName string) (string, error) {
	// 步骤1: 提取关键词
	keywords := cctp.extractKeywords(productName)

	// 步骤2: 将关键词交给AI处理
	coreTag, err := cctp.aiProcessKeywords(keywords, productName)
	if err != nil {
		return "", err
	}

	// 步骤3: 与缓存中的核心标签进行相似度匹配
	unifiedTag := cctp.unifyWithCache(coreTag)

	// 步骤4: 存入缓存
	cctp.addToCache(coreTag, unifiedTag)

	return unifiedTag, nil
}

// 其他方法与main.go中的实现相同，但简化了日志输出
func (cctp *CorrectCoreTagProcessorAI) extractKeywords(productName string) []string {
	// 移除噪音词
	cleaned := cctp.removeNoiseWords(productName)

	// 提取医疗用品关键词
	medicalKeywords := []string{
		"生理盐水", "生理性盐水", "氯化钠", "盐水", "注射液", "洗液", "冲洗液",
		"精华液", "精华", "原液", "精华素", "精华水", "美容液",
		"疤痕贴", "瘢痕贴", "疤痕敷贴", "瘢痕敷贴", "疤痕修复贴", "疤痕贴片",
		"坐便椅", "坐便器", "坐便凳", "大便椅", "厕所椅", "马桶椅", "移动马桶",
		"医用棉签", "棉棒", "棉签", "碘伏棉棒", "碘伏棉签", "消毒棉签",
		"创可贴", "创口贴", "止血贴", "创伤贴", "胶布贴",
		"消毒液", "消毒剂", "酒精", "碘伏", "洗手液",
		"口罩", "防护口罩", "医用口罩", "N95", "KN95",
		"纱布", "医用纱布", "无菌纱布", "敷料",
		"绷带", "弹性绷带", "医用绷带", "包扎带",
	}

	var found []string
	for _, keyword := range medicalKeywords {
		if strings.Contains(cleaned, keyword) {
			found = append(found, keyword)
		}
	}

	// 如果没有找到医疗关键词，提取通用关键词
	if len(found) == 0 {
		words := strings.Fields(cleaned)
		//nolint:gocritic // 保持兼容性，不使用 FieldsSeq
		for _, word := range words {
			if len(word) >= 2 {
				found = append(found, word)
			}
		}
	}

	return found
}

func (cctp *CorrectCoreTagProcessorAI) removeNoiseWords(text string) string {
	noiseWords := []string{
		// 品牌相关
		"妮蓓莉", "形象美", "素秀臣氏", "海氏海诺", "稳健", "云南白药",
		// 规格相关
		"ml", "支", "盒", "瓶", "袋", "包", "个", "片", "粒", "罐", "筒", "条", "贴", "枚",
		"cm", "mm", "m", "g", "mg", "kg", "%", "℃",
		// 用途描述
		"洗鼻", "洗眼", "纹绣", "专用", "清洁", "微针", "控油", "去痘", "保湿", "滋润",
		"抗皱", "补水", "亮肤", "细致", "毛孔", "淡化", "痘印", "收缩", "提亮",
		// 修饰词
		"医用", "一次性", "无菌", "透明", "便携", "家用", "专业", "高级", "进口",
		// 数字和符号
		"0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
		".", "_", "-", "+", "×", "*", "/", "[", "]", "【", "】", "(", ")",
		// 常见词
		"的", "和", "与", "及", "或", "等", "类", "型", "款", "版", "装",
	}

	cleaned := text
	for _, noise := range noiseWords {
		cleaned = strings.ReplaceAll(cleaned, noise, " ")
	}

	// 清理多余空格
	cleaned = strings.Join(strings.Fields(cleaned), " ")
	return strings.TrimSpace(cleaned)
}

func (cctp *CorrectCoreTagProcessorAI) aiProcessKeywords(keywords []string, _ string) (string, error) {
	// 构建简化的商品描述
	keywordText := strings.Join(keywords, " ")

	// 创建简化的商品对象
	product := db.Product{
		ProductName: keywordText,
	}

	// 调用AI处理
	ctx := context.Background()
	result, _, err := cctp.aiClient.GenerateTags(ctx, product, []db.Product{})
	if err != nil {
		return "", err
	}

	return result.CoreTag, nil
}

func (cctp *CorrectCoreTagProcessorAI) unifyWithCache(newCoreTag string) string {
	cctp.mutex.RLock()
	defer cctp.mutex.RUnlock()

	bestMatch := ""
	bestSimilarity := 0.0

	// 遍历缓存中的所有核心标签
	for cachedTag, unifiedTag := range cctp.coreTagCache {
		similarity := cctp.calculateCoreTagSimilarity(newCoreTag, cachedTag)

		if similarity >= cctp.threshold && similarity > bestSimilarity {
			bestSimilarity = similarity
			bestMatch = unifiedTag
		}
	}

	if bestMatch != "" {
		return bestMatch
	}

	return newCoreTag
}

func (cctp *CorrectCoreTagProcessorAI) calculateCoreTagSimilarity(tag1, tag2 string) float64 {
	if tag1 == tag2 {
		return 1.0
	}

	// 特殊相似对
	similarPairs := [][]string{
		{"生理盐水", "生理性盐水"},
		{"生理盐水", "氯化钠"},
		{"生理盐水", "盐水"},
		{"精华液", "精华"},
		{"精华液", "原液"},
		{"疤痕贴", "瘢痕贴"},
		{"疤痕贴", "疤痕敷贴"},
		{"坐便椅", "坐便器"},
		{"坐便椅", "坐便凳"},
		{"医用棉签", "棉棒"},
		{"医用棉签", "棉签"},
		{"创可贴", "创口贴"},
	}

	for _, pair := range similarPairs {
		if (tag1 == pair[0] && tag2 == pair[1]) || (tag1 == pair[1] && tag2 == pair[0]) {
			return 0.95
		}
	}

	// 包含关系检查
	if strings.Contains(tag1, tag2) || strings.Contains(tag2, tag1) {
		return 0.8
	}

	return 0.0
}

func (cctp *CorrectCoreTagProcessorAI) addToCache(originalTag, unifiedTag string) {
	cctp.mutex.Lock()
	defer cctp.mutex.Unlock()

	cctp.coreTagCache[originalTag] = unifiedTag
}

// ExtractBrandFromMapping 基于品牌映射文件提取品牌
func ExtractBrandFromMapping(name string) string {
	// 确保品牌映射已加载
	if err := LoadBrandMapping(); err != nil {
		fmt.Printf("品牌映射加载失败: %v\n", err)
		return ""
	}

	brandMutex.RLock()
	defer brandMutex.RUnlock()

	// 清理商品名称
	cleanName := strings.ToLower(name)

	// 首先尝试从各种括号格式中提取品牌
	fmt.Printf("[ExtractBrandFromMapping] 开始括号品牌提取\n")

	// 1. 尝试花括号 {品牌名}
	if curlyBrand := extractCurlyBracketBrand(name); curlyBrand != "" {
		fmt.Printf("[ExtractBrandFromMapping] 花括号提取: '%s'\n", curlyBrand)
		for key, value := range brandMapping.Mappings {
			if strings.EqualFold(key, curlyBrand) {
				fmt.Printf("品牌映射匹配(花括号): '%s' → '%s' (匹配键: %s)\n", name, value, key)
				return value
			}
		}
		// 括号提取成功但映射失败，应该触发AI重新提取
		fmt.Printf("[ExtractBrandFromMapping] 花括号提取成功但映射失败，返回空触发AI提取: '%s'\n", curlyBrand)
		return ""
	}

	// 2. 尝试中文方括号 【品牌名】
	if chineseBrand := ExtractChineseBracketBrand(name); chineseBrand != "" {
		fmt.Printf("[ExtractBrandFromMapping] 中文括号提取: '%s'\n", chineseBrand)
		for key, value := range brandMapping.Mappings {
			if strings.EqualFold(key, chineseBrand) {
				fmt.Printf("品牌映射匹配(中文括号): '%s' → '%s' (匹配键: %s)\n", name, value, key)
				return value
			}
		}
		// 括号提取成功但映射失败，应该触发AI重新提取
		fmt.Printf("[ExtractBrandFromMapping] 中文括号提取成功但映射失败，返回空触发AI提取: '%s'\n", chineseBrand)
		return ""
	}

	// 3. 尝试特殊引号 「品牌名」
	if specialBrand := extractSpecialQuoteBrand(name); specialBrand != "" {
		fmt.Printf("[ExtractBrandFromMapping] 特殊引号提取: '%s'\n", specialBrand)
		for key, value := range brandMapping.Mappings {
			if strings.EqualFold(key, specialBrand) {
				fmt.Printf("品牌映射匹配(特殊引号): '%s' → '%s' (匹配键: %s)\n", name, value, key)
				return value
			}
		}
		// 括号提取成功但映射失败，应该触发AI重新提取
		fmt.Printf("[ExtractBrandFromMapping] 特殊引号提取成功但映射失败，返回空触发AI提取: '%s'\n", specialBrand)
		return ""
	}

	// 4. 尝试书名号 〈品牌名〉
	if angleBrand := extractAngleBracketBrand(name); angleBrand != "" {
		fmt.Printf("[ExtractBrandFromMapping] 书名号提取: '%s'\n", angleBrand)
		for key, value := range brandMapping.Mappings {
			if strings.EqualFold(key, angleBrand) {
				fmt.Printf("品牌映射匹配(书名号): '%s' → '%s' (匹配键: %s)\n", name, value, key)
				return value
			}
		}
		// 括号提取成功但映射失败，应该触发AI重新提取
		fmt.Printf("[ExtractBrandFromMapping] 书名号提取成功但映射失败，返回空触发AI提取: '%s'\n", angleBrand)
		return ""
	}

	// 5. 尝试方括号 [品牌名]
	if bracketBrand := ExtractBracketBrand(name); bracketBrand != "" {
		fmt.Printf("[ExtractBrandFromMapping] 方括号提取: '%s'\n", bracketBrand)
		for key, value := range brandMapping.Mappings {
			if strings.EqualFold(key, bracketBrand) {
				fmt.Printf("品牌映射匹配(方括号): '%s' → '%s' (匹配键: %s)\n", name, value, key)
				return value
			}
		}
		// 括号提取成功但映射失败，应该触发AI重新提取
		fmt.Printf("[ExtractBrandFromMapping] 方括号提取成功但映射失败，返回空触发AI提取: '%s'\n", bracketBrand)
		return ""
	}

	// 移除噪声词
	for _, noise := range brandMapping.NoiseWords {
		cleanName = strings.ReplaceAll(cleanName, strings.ToLower(noise), "")
	}

	// 按品牌名长度排序，优先匹配长品牌名
	type brandMatch struct {
		key    string
		value  string
		length int
	}

	var matches []brandMatch

	// 查找所有可能的品牌匹配
	for key, value := range brandMapping.Mappings {
		lowerKey := strings.ToLower(key)

		// 检查商品名是否包含品牌关键词
		if strings.Contains(cleanName, lowerKey) {
			matches = append(matches, brandMatch{
				key:    key,
				value:  value,
				length: len(lowerKey),
			})
		}
	}

	// 如果没有匹配，返回空
	if len(matches) == 0 {
		return ""
	}

	// 按长度排序，优先返回最长匹配（更精确）
	sort.Slice(matches, func(i, j int) bool {
		return matches[i].length > matches[j].length
	})

	bestMatch := matches[0]
	fmt.Printf("品牌映射匹配: '%s' → '%s' (匹配键: %s, 长度: %d)\n",
		name, bestMatch.value, bestMatch.key, bestMatch.length)

	return bestMatch.value
}

// StandardizeBrandWithMapping 使用品牌映射文件对AI提取的品牌进行标准化
func StandardizeBrandWithMapping(aiBrand string) string {
	// 确保品牌映射已加载
	if err := LoadBrandMapping(); err != nil {
		fmt.Printf("品牌映射加载失败: %v\n", err)
		return aiBrand // 映射失败时返回原品牌
	}

	brandMutex.RLock()
	defer brandMutex.RUnlock()

	// 清理AI品牌名称
	cleanAIBrand := strings.ToLower(strings.TrimSpace(aiBrand))

	// 在品牌映射中查找匹配项（最长匹配优先）
	var bestMatch string
	var bestKey string
	maxLength := 0

	for key, value := range brandMapping.Mappings {
		lowerKey := strings.ToLower(key)

		// 精确匹配（最高优先级）
		if lowerKey == cleanAIBrand {
			fmt.Printf("[StandardizeBrand] 精确匹配: '%s' → '%s'\n", aiBrand, value)
			return value
		}

		// 包含匹配（AI品牌包含映射键）
		if strings.Contains(cleanAIBrand, lowerKey) && len(lowerKey) > maxLength {
			bestMatch = value
			bestKey = key
			maxLength = len(lowerKey)
		}

		// 反向包含匹配（映射键包含AI品牌）
		if strings.Contains(lowerKey, cleanAIBrand) && len(cleanAIBrand) > maxLength {
			bestMatch = value
			bestKey = key
			maxLength = len(cleanAIBrand)
		}
	}

	// 返回最佳匹配
	if bestMatch != "" {
		fmt.Printf("[StandardizeBrand] 最长匹配: '%s' → '%s' (匹配键: %s, 长度: %d)\n", aiBrand, bestMatch, bestKey, maxLength)
		return bestMatch
	}

	// 没有找到匹配项，返回原AI品牌
	fmt.Printf("[StandardizeBrand] 无匹配项，保持原品牌: '%s'\n", aiBrand)
	return aiBrand
}
