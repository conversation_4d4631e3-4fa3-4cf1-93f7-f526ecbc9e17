package excel

import (
	"fmt"
	"strings"

	"github.com/mogan/tag-assistant/ai"
	"github.com/mogan/tag-assistant/db"
	"github.com/mogan/tag-assistant/db/mapping"
	"github.com/mogan/tag-assistant/utils" // 添加utils包导入
	"github.com/xuri/excelize/v2"
)

// 删除 PrintVersion 函数，因为它没有被使用

func PrintProducts(products []db.Product) {
	for i, p := range products {
		fmt.Printf("%d. %s (ID: %d)\n", i+1, p.ProductName, p.ID)
		fmt.Printf("  核心标签: %s\n", p.CoreTag)
		fmt.Printf("  清洗品名: %s\n", p.CleanedName)
		fmt.Printf("  类目: %s/%s\n",
			strings.Join(p.Category1, ", "),
			strings.Join(p.Category2, ", "))
		fmt.Printf("  场景: %s, 人群: %s\n", p.Scene, p.Crowd)
		fmt.Println()
	}
}
func ReadExcel(filePath string) ([]db.Product, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, err
	}

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var products []db.Product

	isMultiColumn := false
	if len(rows) > 0 {
		headers := rows[0]
		if len(headers) >= 8 &&
			strings.Contains(headers[0], "商品") &&
			strings.Contains(headers[1], "核心") {
			isMultiColumn = true
		}
	}

	for i, row := range rows {
		if i == 0 {
			continue
		}

		if len(row) == 0 || strings.TrimSpace(row[0]) == "" {
			fmt.Printf("! 跳过空行: 第 %d 行\n", i+1)
			continue
		}

		p := db.Product{}

		if isMultiColumn {
			if len(row) > 0 {
				p.ProductName = strings.TrimSpace(row[0])
			}
			if len(row) > 1 {
				p.CoreTag = strings.TrimSpace(row[1])
			}
			if len(row) > 2 {
				p.Scene = strings.TrimSpace(row[2])
			}
			if len(row) > 3 {
				p.Crowd = strings.TrimSpace(row[3])
			}
			if len(row) > 4 {
				// 确保导入时品牌名使用UTF-8编码
				brand := strings.TrimSpace(row[4])
				p.Brand = utils.EnsureUTF8(brand)
				fmt.Printf("导入品牌名: %s -> %s\n", row[4], p.Brand) // 添加调试日志
			}
			if len(row) > 5 {
				p.Spec = strings.TrimSpace(row[5])
			}
			if len(row) > 6 {
				cats := strings.Split(strings.TrimSpace(row[6]), ",")
				if len(cats) > 2 {
					cats = cats[:2]
				}
				p.Category1 = cats
			}
			if len(row) > 7 {
				cats := strings.Split(strings.TrimSpace(row[7]), ",")
				if len(cats) > 2 {
					cats = cats[:2]
				}
				p.Category2 = cats
			}
		} else {
			if len(row) < 1 {
				fmt.Printf("! 警告: 第 %d 行没有商品名称，跳过处理\n", i+1)
				continue
			}
			p.ProductName = strings.TrimSpace(row[0])
		}

		if len(p.Category2) > 0 && len(p.Category1) == 0 {
			p.Category1 = mapping.GetCategories1(p.Category2)
		}
		products = append(products, p)
	}

	fmt.Printf("✓ 读取文件成功: %d 行数据 (%s格式)\n",
		len(products),
		map[bool]string{true: "完整表头", false: "单列"}[isMultiColumn])

	return products, nil
}

func SplitMultiCategories(products []db.Product, keepCombined bool) []db.Product {
	if keepCombined {
		return products
	}
	for i := range products {
		if len(products[i].Category2) > 2 {
			products[i].Category2 = products[i].Category2[:2]
		}
	}
	return products
}

// 修改WriteExcel函数
func WriteExcel(products []db.Product, filePath string, keepCombined bool) error {
	// 品牌统计监控
	brandStats := make(map[string]int)
	emptyBrandCount := 0
	totalCount := len(products)

	fmt.Printf("\n=== 品牌导出统计监控 ===\n")
	fmt.Printf("总产品数: %d\n", totalCount)

	// 过滤掉二级类目与商品名称完全一致的商品
	var filteredProducts []db.Product
	for _, p := range products {
		if len(p.Category2) == 1 && p.Category2[0] == p.ProductName {
			continue
		}
		filteredProducts = append(filteredProducts, p)

		// 统计品牌
		if p.Brand == "" || p.Brand == "无品牌" {
			emptyBrandCount++
		} else {
			brandStats[p.Brand]++
		}
	}

	splitProducts := SplitMultiCategories(filteredProducts, keepCombined)
	f := excelize.NewFile()

	// 设置UTF-8编码标识（使用正确的API）
	codeName := "UTF-8"
	f.SetWorkbookProps(&excelize.WorkbookPropsOptions{
		CodeName: &codeName,
	})

	// 创建支持中文的字体样式
	chineseFontStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Family: "Microsoft YaHei", // 使用微软雅黑字体
			Size:   11,
		},
	})
	if err != nil {
		fmt.Printf("警告: 创建中文字体样式失败: %v\n", err)
	}

	// 修复点 1: NewSheet 现在返回两个值
	index, err := f.NewSheet("Sheet1")
	if err != nil {
		return fmt.Errorf("创建Sheet失败: %w", err)
	}
	// 修复点 2: 使用新的 API 设置单元格值
	headers := []string{
		"商品名称", "核心标签", "场景标签", "人群标签", "品牌名",
		"规格", "一级类目", "二级类目", "品名清洗", "特征标签",
	}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		if err := f.SetCellValue("Sheet1", cell, header); err != nil {
			return fmt.Errorf("设置表头失败: %w", err)
		}
	}

	rowIndex := 2
	for _, p := range splitProducts {
		// 处理多个二级类目
		if len(p.Category2) > 0 {
			for _, cat2 := range p.Category2 {
				// 跳过与商品名称完全一致的二级类目
				if cat2 == p.ProductName {
					continue
				}

				cat1 := ""
				if len(p.Category1) > 0 {
					// 优先使用商品已有的一级类目（如果只有一个）
					if len(p.Category1) == 1 {
						cat1 = p.Category1[0]
					} else {
						// 多个一级类目时，根据二级类目查找对应的一级类目
						cat1 = mapping.GetCategory1(cat2)
					}
				} else {
					// 商品没有一级类目时，根据二级类目查找
					cat1 = mapping.GetCategory1(cat2)
				}

				// === 🔍 完整Excel导出监控 ===
				fmt.Printf("\n[监控-EXCEL-导出] 开始导出产品: '%s'\n", p.ProductName)

				// 监控商品名称导出
				f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), p.ProductName)
				fmt.Printf("[监控-EXCEL-A] 商品名称: '%s' → A%d\n", p.ProductName, rowIndex)

				// 监控核心标签导出
				f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowIndex), p.CoreTag)
				fmt.Printf("[监控-EXCEL-B] 核心标签: '%s' → B%d\n", p.CoreTag, rowIndex)

				// 监控场景标签导出
				f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowIndex), p.Scene)
				fmt.Printf("[监控-EXCEL-C] 场景标签: '%s' → C%d\n", p.Scene, rowIndex)

				// 监控人群标签导出
				f.SetCellValue("Sheet1", fmt.Sprintf("D%d", rowIndex), p.Crowd)
				fmt.Printf("[监控-EXCEL-D] 人群标签: '%s' → D%d\n", p.Crowd, rowIndex)

				// === 🔧 Excel导出品牌修复机制(使用AI提取) ===
				fmt.Printf("[Excel品牌修复-1] 原始品牌: '%s'\n", p.Brand)

				// 检测空品牌并尝试修复
				brand := p.Brand
				if brand == "" || brand == "无品牌" {
					fmt.Printf("[Excel品牌修复-2] ⚠️ 检测到空品牌，启动AI品牌提取\n")

					// 使用修复后的AI品牌提取函数
					extractedBrand := ai.ExtractBrand(p.ProductName)
					if extractedBrand != "" && extractedBrand != "无品牌" {
						brand = extractedBrand
						fmt.Printf("[Excel品牌修复-3] ✅ AI提取成功: '%s'\n", brand)
					} else {
						fmt.Printf("[Excel品牌修复-4] ❌ AI提取失败，保持空值\n")
					}
				} else {
					fmt.Printf("[Excel品牌修复-2] ✅ 品牌有效: '%s'\n", brand)
				}

				// UTF8处理
				utf8Brand := utils.EnsureUTF8(brand)
				fmt.Printf("[Excel品牌修复-6] UTF8处理: '%s' → '%s'\n", brand, utf8Brand)

				// 写入Excel
				f.SetCellStr("Sheet1", fmt.Sprintf("E%d", rowIndex), utf8Brand)
				fmt.Printf("[Excel品牌修复-7] 品牌写入Excel: '%s' → E%d\n", utf8Brand, rowIndex)

				// 应用中文字体样式
				if err := f.SetCellStyle("Sheet1", fmt.Sprintf("E%d", rowIndex), fmt.Sprintf("E%d", rowIndex), chineseFontStyle); err != nil {
					fmt.Printf("警告: 设置单元格样式失败: %v\n", err)
				}

				// 监控规格导出
				f.SetCellValue("Sheet1", fmt.Sprintf("F%d", rowIndex), p.Spec)
				fmt.Printf("[监控-EXCEL-F] 规格: '%s' → F%d\n", p.Spec, rowIndex)

				// 监控一级类目导出
				f.SetCellValue("Sheet1", fmt.Sprintf("G%d", rowIndex), cat1)
				fmt.Printf("[监控-EXCEL-G] 一级类目: '%s' → G%d\n", cat1, rowIndex)

				// 监控二级类目导出
				f.SetCellValue("Sheet1", fmt.Sprintf("H%d", rowIndex), cat2)
				fmt.Printf("[监控-EXCEL-H] 二级类目: '%s' → H%d\n", cat2, rowIndex)

				// 监控清洗品名导出（优化格式：品牌+核心标签+规格）
				cleanedName := utf8Brand + "+" + p.CoreTag + "+" + p.Spec
				f.SetCellValue("Sheet1", fmt.Sprintf("I%d", rowIndex), cleanedName)
				fmt.Printf("[监控-EXCEL-I] 清洗品名: '%s' → I%d\n", cleanedName, rowIndex)

				// 监控特征标签导出
				featureTags := strings.Join(p.FeatureTags, ",")
				f.SetCellValue("Sheet1", fmt.Sprintf("J%d", rowIndex), featureTags)
				fmt.Printf("[监控-EXCEL-J] 特征标签: '%s' → J%d\n", featureTags, rowIndex)

				fmt.Printf("[监控-EXCEL-完成] 产品 '%s' 导出完成，行号: %d\n", p.ProductName, rowIndex)
				rowIndex++
			}
		} else {
			// 没有二级类目的情况
			fmt.Printf("\n[监控-EXCEL-导出] 开始导出产品(无二级类目): '%s'\n", p.ProductName)

			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), p.ProductName)
			fmt.Printf("[监控-EXCEL-A] 商品名称: '%s' → A%d\n", p.ProductName, rowIndex)

			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowIndex), p.CoreTag)
			fmt.Printf("[监控-EXCEL-B] 核心标签: '%s' → B%d\n", p.CoreTag, rowIndex)

			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowIndex), p.Scene)
			fmt.Printf("[监控-EXCEL-C] 场景标签: '%s' → C%d\n", p.Scene, rowIndex)

			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", rowIndex), p.Crowd)
			fmt.Printf("[监控-EXCEL-D] 人群标签: '%s' → D%d\n", p.Crowd, rowIndex)

			// === 🔧 Excel导出品牌修复机制(无二级类目-使用AI提取) ===
			fmt.Printf("[Excel品牌修复-无类目-1] 原始品牌: '%s'\n", p.Brand)

			// 检测空品牌并尝试修复
			brand := p.Brand
			if brand == "" || brand == "无品牌" {
				fmt.Printf("[Excel品牌修复-无类目-2] ⚠️ 检测到空品牌，启动AI品牌提取\n")

				// 使用修复后的AI品牌提取函数
				extractedBrand := ai.ExtractBrand(p.ProductName)
				if extractedBrand != "" && extractedBrand != "无品牌" {
					brand = extractedBrand
					fmt.Printf("[Excel品牌修复-无类目-3] ✅ AI提取成功: '%s'\n", brand)
				} else {
					fmt.Printf("[Excel品牌修复-无类目-4] ❌ AI提取失败，保持空值\n")
				}
			} else {
				fmt.Printf("[Excel品牌修复-无类目-2] ✅ 品牌有效: '%s'\n", brand)
			}

			// UTF8处理
			utf8Brand := utils.EnsureUTF8(brand)
			fmt.Printf("[Excel品牌修复-无类目-6] UTF8处理: '%s' → '%s'\n", brand, utf8Brand)

			// 写入Excel
			f.SetCellStr("Sheet1", fmt.Sprintf("E%d", rowIndex), utf8Brand)
			fmt.Printf("[Excel品牌修复-无类目-7] 品牌写入Excel: '%s' → E%d\n", utf8Brand, rowIndex)

			// 应用中文字体样式
			if err := f.SetCellStyle("Sheet1", fmt.Sprintf("E%d", rowIndex), fmt.Sprintf("E%d", rowIndex), chineseFontStyle); err != nil {
				fmt.Printf("警告: 设置单元格样式失败: %v\n", err)
			}

			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", rowIndex), p.Spec)
			fmt.Printf("[监控-EXCEL-F] 规格: '%s' → F%d\n", p.Spec, rowIndex)

			f.SetCellValue("Sheet1", fmt.Sprintf("G%d", rowIndex), "")
			f.SetCellValue("Sheet1", fmt.Sprintf("H%d", rowIndex), "")
			fmt.Printf("[监控-EXCEL-GH] 类目: 空 → G%d,H%d\n", rowIndex, rowIndex)

			// 清洗品名导出（优化格式：品牌+核心标签+规格）
			cleanedName := utf8Brand + "+" + p.CoreTag + "+" + p.Spec
			f.SetCellValue("Sheet1", fmt.Sprintf("I%d", rowIndex), cleanedName)
			fmt.Printf("[监控-EXCEL-I] 清洗品名: '%s' → I%d\n", cleanedName, rowIndex)

			featureTags := strings.Join(p.FeatureTags, ",")
			f.SetCellValue("Sheet1", fmt.Sprintf("J%d", rowIndex), featureTags)
			fmt.Printf("[监控-EXCEL-J] 特征标签: '%s' → J%d\n", featureTags, rowIndex)

			fmt.Printf("[监控-EXCEL-完成] 产品 '%s' 导出完成，行号: %d\n", p.ProductName, rowIndex)
			rowIndex++
		}
	}

	f.SetActiveSheet(index)

	// 输出品牌统计报告
	fmt.Printf("\n=== 品牌导出完成统计 ===\n")
	fmt.Printf("空品牌产品数: %d (%.1f%%)\n", emptyBrandCount, float64(emptyBrandCount)/float64(len(filteredProducts))*100)
	fmt.Printf("有效品牌产品数: %d (%.1f%%)\n", len(filteredProducts)-emptyBrandCount, float64(len(filteredProducts)-emptyBrandCount)/float64(len(filteredProducts))*100)
	fmt.Printf("品牌种类数: %d\n", len(brandStats))

	// 显示前10个最常见的品牌
	type brandCount struct {
		brand string
		count int
	}
	var sortedBrands []brandCount
	for brand, count := range brandStats {
		sortedBrands = append(sortedBrands, brandCount{brand, count})
	}

	// 简单排序（按数量降序）
	for i := 0; i < len(sortedBrands)-1; i++ {
		for j := i + 1; j < len(sortedBrands); j++ {
			if sortedBrands[j].count > sortedBrands[i].count {
				sortedBrands[i], sortedBrands[j] = sortedBrands[j], sortedBrands[i]
			}
		}
	}

	fmt.Printf("前10个最常见品牌:\n")
	for i, bc := range sortedBrands {
		if i >= 10 {
			break
		}
		fmt.Printf("  %d. %s: %d个产品\n", i+1, bc.brand, bc.count)
	}
	fmt.Printf("=== 品牌统计结束 ===\n\n")

	if err := f.SaveAs(filePath); err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}
	return nil
}
